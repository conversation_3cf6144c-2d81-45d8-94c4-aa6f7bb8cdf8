{"config": {"filename": "FileScopeMCP-tree-JYZS.json", "baseDirectory": "E:/JYZS", "projectRoot": "E:/JYZS", "lastUpdated": "2025-08-03T11:26:34.236Z"}, "fileTree": {"path": "E:\\JYZS", "name": "JYZS", "isDirectory": true, "children": [{"path": "E:\\JYZS\\.cunzhi-memory", "name": ".cunzhi-memory", "isDirectory": true, "children": [{"path": "E:\\JYZS\\.cunzhi-memory\\context.md", "name": "context.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\.cunzhi-memory\\metadata.json", "name": "metadata.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\.cunzhi-memory\\patterns.md", "name": "patterns.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\.cunzhi-memory\\preferences.md", "name": "preferences.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\.cunzhi-memory\\rules.md", "name": "rules.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\.idea", "name": ".idea", "isDirectory": true, "children": [{"path": "E:\\JYZS\\.idea\\AugmentWebviewStateStore.xml", "name": "AugmentWebviewStateStore.xml", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\.idea\\vcs.xml", "name": "vcs.xml", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\.idea\\workspace.xml", "name": "workspace.xml", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend", "name": "ebbinghaus-backend", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\.env", "name": ".env", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\.env.development", "name": ".env.development", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\.env.example", "name": ".env.example", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\.env.production", "name": ".env.production", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\.env.test", "name": ".env.test", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage", "name": "coverage", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\base.css", "name": "base.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\block-navigation.js", "name": "block-navigation.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\favicon.png", "name": "favicon.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report", "name": "lcov-report", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\base.css", "name": "base.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\block-navigation.js", "name": "block-navigation.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\favicon.png", "name": "favicon.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\prettify.css", "name": "prettify.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\prettify.js", "name": "prettify.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\sort-arrow-sprite.png", "name": "sort-arrow-sprite.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\sorter.js", "name": "sorter.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src", "name": "src", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\app.js.html", "name": "app.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\config", "name": "config", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\config\\database.js.html", "name": "database.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\config\\environment.js.html", "name": "environment.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\config\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\config\\logger.js.html", "name": "logger.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\config\\performance.js.html", "name": "performance.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\config\\redis.js.html", "name": "redis.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\config\\swagger.js.html", "name": "swagger.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\controllers", "name": "controllers", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\controllers\\analyticsController.js.html", "name": "analyticsController.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\controllers\\authController.js.html", "name": "authController.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\controllers\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\controllers\\reviewController.js.html", "name": "reviewController.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\controllers\\taskController.js.html", "name": "taskController.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\middleware", "name": "middleware", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\middleware\\auth.js.html", "name": "auth.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\middleware\\errorHandler.js.html", "name": "errorHandler.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\middleware\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\middleware\\rateLimit.js.html", "name": "rateLimit.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\middleware\\validation.js.html", "name": "validation.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\models", "name": "models", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\models\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\models\\LearningRecord.js.html", "name": "LearningRecord.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\models\\ReviewSchedule.js.html", "name": "ReviewSchedule.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\models\\Task.js.html", "name": "Task.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\models\\User.js.html", "name": "User.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\routes", "name": "routes", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\routes\\analytics.js.html", "name": "analytics.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\routes\\auth.js.html", "name": "auth.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\routes\\docs.js.html", "name": "docs.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\routes\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\routes\\reviews.js.html", "name": "reviews.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\routes\\tasks.js.html", "name": "tasks.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\services", "name": "services", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\services\\authService.js.html", "name": "authService.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\services\\ebbinghausService.js.html", "name": "ebbinghausService.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\services\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\services\\loadBalanceService.js.html", "name": "loadBalanceService.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\services\\taskService.js.html", "name": "taskService.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\utils", "name": "utils", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\utils\\constants.js.html", "name": "constants.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\utils\\helpers.js.html", "name": "helpers.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\utils\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov.info", "name": "lcov.info", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\prettify.css", "name": "prettify.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\prettify.js", "name": "prettify.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\sort-arrow-sprite.png", "name": "sort-arrow-sprite.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\sorter.js", "name": "sorter.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src", "name": "src", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\app.js.html", "name": "app.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\config", "name": "config", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\config\\database.js.html", "name": "database.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\config\\environment.js.html", "name": "environment.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\config\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\config\\logger.js.html", "name": "logger.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\config\\performance.js.html", "name": "performance.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\config\\redis.js.html", "name": "redis.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\config\\swagger.js.html", "name": "swagger.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\controllers", "name": "controllers", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\controllers\\analyticsController.js.html", "name": "analyticsController.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\controllers\\authController.js.html", "name": "authController.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\controllers\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\controllers\\reviewController.js.html", "name": "reviewController.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\controllers\\taskController.js.html", "name": "taskController.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\middleware", "name": "middleware", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\middleware\\auth.js.html", "name": "auth.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\middleware\\errorHandler.js.html", "name": "errorHandler.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\middleware\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\middleware\\rateLimit.js.html", "name": "rateLimit.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\middleware\\validation.js.html", "name": "validation.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\models", "name": "models", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\models\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\models\\LearningRecord.js.html", "name": "LearningRecord.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\models\\ReviewSchedule.js.html", "name": "ReviewSchedule.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\models\\Task.js.html", "name": "Task.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\models\\User.js.html", "name": "User.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\routes", "name": "routes", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\routes\\analytics.js.html", "name": "analytics.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\routes\\auth.js.html", "name": "auth.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\routes\\docs.js.html", "name": "docs.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\routes\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\routes\\reviews.js.html", "name": "reviews.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\routes\\tasks.js.html", "name": "tasks.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\services", "name": "services", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\services\\authService.js.html", "name": "authService.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\services\\ebbinghausService.js.html", "name": "ebbinghausService.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\services\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\services\\loadBalanceService.js.html", "name": "loadBalanceService.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\services\\taskService.js.html", "name": "taskService.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\utils", "name": "utils", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\utils\\constants.js.html", "name": "constants.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\utils\\helpers.js.html", "name": "helpers.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\utils\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\docs", "name": "docs", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\docs\\API.md", "name": "API.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\docs\\API快速参考.md", "name": "API快速参考.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\docs\\swagger-validation-report.json", "name": "swagger-validation-report.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\docs\\SWAGGER_SETUP.md", "name": "SWAGGER_SETUP.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\docs\\后端接口.md", "name": "后端接口.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\ecosystem.config.js", "name": "ecosystem.config.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\jest.config.js", "name": "jest.config.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\logs", "name": "logs", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\logs\\.a9826a81bd0a53dae5fa25eb83576f18774a9171-audit.json", "name": ".a9826a81bd0a53dae5fa25eb83576f18774a9171-audit.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\logs\\.f061ad709625724e064f5ead54e924ca25eb8ef3-audit.json", "name": ".f061ad709625724e064f5ead54e924ca25eb8ef3-audit.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\logs\\combined-2025-08-03.log", "name": "combined-2025-08-03.log", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\logs\\error-2025-08-03.log", "name": "error-2025-08-03.log", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\package-lock.json", "name": "package-lock.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\package.json", "name": "package.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\PRODUCTION.md", "name": "PRODUCTION.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\README.md", "name": "README.md", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\scripts", "name": "scripts", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\scripts\\backup.js", "name": "backup.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [{"name": "child_process", "path": "E:\\JYZS\\node_modules\\child_process"}, {"name": "fs", "path": "E:\\JYZS\\node_modules\\fs"}, {"name": "path", "path": "E:\\JYZS\\node_modules\\path"}, {"name": "util", "path": "E:\\JYZS\\node_modules\\util"}, {"name": "dotenv", "path": "E:\\JYZS\\node_modules\\dotenv"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\scripts\\deploy.sh", "name": "deploy.sh", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\scripts\\migrate.js", "name": "migrate.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [{"name": "mongoose", "path": "E:\\JYZS\\node_modules\\mongoose"}, {"name": "path", "path": "E:\\JYZS\\node_modules\\path"}, {"name": "fs", "path": "E:\\JYZS\\node_modules\\fs"}, {"name": "dotenv", "path": "E:\\JYZS\\node_modules\\dotenv"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\scripts\\restore.js", "name": "restore.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [{"name": "child_process", "path": "E:\\JYZS\\node_modules\\child_process"}, {"name": "fs", "path": "E:\\JYZS\\node_modules\\fs"}, {"name": "path", "path": "E:\\JYZS\\node_modules\\path"}, {"name": "util", "path": "E:\\JYZS\\node_modules\\util"}, {"name": "readline", "path": "E:\\JYZS\\node_modules\\readline"}, {"name": "dotenv", "path": "E:\\JYZS\\node_modules\\dotenv"}, {"name": "mongoose", "path": "E:\\JYZS\\node_modules\\mongoose"}, {"name": "redis", "path": "E:\\JYZS\\node_modules\\redis"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\scripts\\start-production.sh", "name": "start-production.sh", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\scripts\\validate-swagger.js", "name": "validate-swagger.js", "isDirectory": false, "importance": 4, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\config\\swagger.js"], "packageDependencies": [{"name": "fs", "path": "E:\\JYZS\\node_modules\\fs"}, {"name": "path", "path": "E:\\JYZS\\node_modules\\path"}], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src", "name": "src", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "name": "app.js", "isDirectory": false, "importance": 10, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\database.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\redis.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\tasks.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\reviews.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\analytics.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\docs.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js"], "packageDependencies": [{"name": "dotenv", "path": "E:\\JYZS\\node_modules\\dotenv"}, {"name": "express", "path": "E:\\JYZS\\node_modules\\express"}, {"name": "cors", "path": "E:\\JYZS\\node_modules\\cors"}, {"name": "helmet", "path": "E:\\JYZS\\node_modules\\helmet"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\server.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\auth.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\health.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\tasks.test.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\config", "name": "config", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\src\\config\\database.js", "name": "database.js", "isDirectory": false, "importance": 6, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [{"name": "mongoose", "path": "E:\\JYZS\\node_modules\\mongoose"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\server.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\config\\environment.js", "name": "environment.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "path", "path": "E:\\JYZS\\node_modules\\path"}, {"name": "fs", "path": "E:\\JYZS\\node_modules\\fs"}, {"name": "dotenv", "path": "E:\\JYZS\\node_modules\\dotenv"}, {"name": "dotenv", "path": "E:\\JYZS\\node_modules\\dotenv"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\config\\performance.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js", "name": "logger.js", "isDirectory": false, "importance": 6, "dependencies": [], "packageDependencies": [{"name": "winston", "path": "E:\\JYZS\\node_modules\\winston"}, {"name": "winston-daily-rotate-file", "path": "E:\\JYZS\\node_modules\\winston-daily-rotate-file"}, {"name": "path", "path": "E:\\JYZS\\node_modules\\path"}, {"name": "fs", "path": "E:\\JYZS\\node_modules\\fs"}, {"name": "morgan", "path": "E:\\JYZS\\node_modules\\morgan"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\database.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\redis.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\authController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\reviewController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\taskController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\validation.js", "E:\\JYZS\\ebbinghaus-backend\\src\\server.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\authService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\ebbinghausService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\loadBalanceService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js", "E:\\JYZS\\ebbinghaus-backend\\test-server.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\config\\performance.js", "name": "performance.js", "isDirectory": false, "importance": 4, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\config\\environment.js"], "packageDependencies": [{"name": "compression", "path": "E:\\JYZS\\node_modules\\compression"}, {"name": "helmet", "path": "E:\\JYZS\\node_modules\\helmet"}, {"name": "os", "path": "E:\\JYZS\\node_modules\\os"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\config\\redis.js", "name": "redis.js", "isDirectory": false, "importance": 7, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [{"name": "i<PERSON>is", "path": "E:\\JYZS\\node_modules\\ioredis"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js", "E:\\JYZS\\ebbinghaus-backend\\src\\server.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\authService.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\config\\swagger.js", "name": "swagger.js", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [{"name": "swagger-jsdoc", "path": "E:\\JYZS\\node_modules\\swagger-jsdoc"}, {"name": "swagger-ui-express", "path": "E:\\JYZS\\node_modules\\swagger-ui-express"}, {"name": "path", "path": "E:\\JYZS\\node_modules\\path"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\scripts\\validate-swagger.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\docs.js"]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\controllers", "name": "controllers", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "name": "analyticsController.js", "isDirectory": false, "importance": 5, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\models\\User.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\Task.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\ReviewSchedule.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\LearningRecord.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\loadBalanceService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\routes\\analytics.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\authController.js", "name": "authController.js", "isDirectory": false, "importance": 5, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\models\\User.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\authService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\reviewController.js", "name": "reviewController.js", "isDirectory": false, "importance": 5, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\models\\ReviewSchedule.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\ebbinghausService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\routes\\reviews.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\taskController.js", "name": "taskController.js", "isDirectory": false, "importance": 5, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\routes\\tasks.js"]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\middleware", "name": "middleware", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "name": "auth.js", "isDirectory": false, "importance": 7, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\services\\authService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\analytics.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\reviews.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\tasks.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js", "name": "errorHandler.js", "isDirectory": false, "importance": 7, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\analytics.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\reviews.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\tasks.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js", "name": "rateLimit.js", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\redis.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [{"name": "express-rate-limit", "path": "E:\\JYZS\\node_modules\\express-rate-limit"}, {"name": "express-rate-limit", "path": "E:\\JYZS\\node_modules\\express-rate-limit"}, {"name": "rate-limit-redis", "path": "E:\\JYZS\\node_modules\\rate-limit-redis"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\tasks.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\validation.js", "name": "validation.js", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [{"name": "express-validator", "path": "E:\\JYZS\\node_modules\\express-validator"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\routes\\analytics.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\reviews.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\tasks.js"]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\models", "name": "models", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\src\\models\\LearningRecord.js", "name": "LearningRecord.js", "isDirectory": false, "importance": 6, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js"], "packageDependencies": [{"name": "mongoose", "path": "E:\\JYZS\\node_modules\\mongoose"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\models\\ReviewSchedule.js", "name": "ReviewSchedule.js", "isDirectory": false, "importance": 7, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js"], "packageDependencies": [{"name": "mongoose", "path": "E:\\JYZS\\node_modules\\mongoose"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\reviewController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\taskService.test.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\models\\Task.js", "name": "Task.js", "isDirectory": false, "importance": 7, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js"], "packageDependencies": [{"name": "mongoose", "path": "E:\\JYZS\\node_modules\\mongoose"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\tasks.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\taskService.test.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\models\\User.js", "name": "User.js", "isDirectory": false, "importance": 7, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js"], "packageDependencies": [{"name": "mongoose", "path": "E:\\JYZS\\node_modules\\mongoose"}, {"name": "bcryptjs", "path": "E:\\JYZS\\node_modules\\bcryptjs"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\authController.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\auth.test.js"]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\routes", "name": "routes", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\analytics.js", "name": "analytics.js", "isDirectory": false, "importance": 6, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\validation.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js"], "packageDependencies": [{"name": "express", "path": "E:\\JYZS\\node_modules\\express"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js", "name": "auth.js", "isDirectory": false, "importance": 6, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\authController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\validation.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js"], "packageDependencies": [{"name": "express", "path": "E:\\JYZS\\node_modules\\express"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\docs.js", "name": "docs.js", "isDirectory": false, "importance": 5, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\config\\swagger.js"], "packageDependencies": [{"name": "express", "path": "E:\\JYZS\\node_modules\\express"}, {"name": "js-yaml", "path": "E:\\JYZS\\node_modules\\js-yaml"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\reviews.js", "name": "reviews.js", "isDirectory": false, "importance": 6, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\reviewController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\validation.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js"], "packageDependencies": [{"name": "express", "path": "E:\\JYZS\\node_modules\\express"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\tasks.js", "name": "tasks.js", "isDirectory": false, "importance": 6, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\taskController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\validation.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js"], "packageDependencies": [{"name": "express", "path": "E:\\JYZS\\node_modules\\express"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js"]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\server.js", "name": "server.js", "isDirectory": false, "importance": 6, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\database.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\redis.js"], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\services", "name": "services", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\src\\services\\authService.js", "name": "authService.js", "isDirectory": false, "importance": 7, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\redis.js"], "packageDependencies": [{"name": "jsonwebtoken", "path": "E:\\JYZS\\node_modules\\jsonwebtoken"}, {"name": "bcryptjs", "path": "E:\\JYZS\\node_modules\\bcryptjs"}, {"name": "crypto", "path": "E:\\JYZS\\node_modules\\crypto"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\authController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\services\\ebbinghausService.js", "name": "ebbinghausService.js", "isDirectory": false, "importance": 7, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\reviewController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\ebbinghausService.simple.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\ebbinghausService.test.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\services\\loadBalanceService.js", "name": "loadBalanceService.js", "isDirectory": false, "importance": 6, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js", "name": "taskService.js", "isDirectory": false, "importance": 6, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\models\\Task.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\ReviewSchedule.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\LearningRecord.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\ebbinghausService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\loadBalanceService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\taskController.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\taskService.test.js"]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\utils", "name": "utils", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "name": "constants.js", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\authController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\reviewController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\taskController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\validation.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\LearningRecord.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\ReviewSchedule.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\Task.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\User.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\authService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\ebbinghausService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\loadBalanceService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "E:\\JYZS\\ebbinghaus-backend\\test-server.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "name": "helpers.js", "isDirectory": false, "importance": 7, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js"], "packageDependencies": [{"name": "uuid", "path": "E:\\JYZS\\node_modules\\uuid"}, {"name": "dayjs", "path": "E:\\JYZS\\node_modules\\dayjs"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\authController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\reviewController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\authService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\ebbinghausService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\loadBalanceService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js"]}]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\test-db.js", "name": "test-db.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [{"name": "dotenv", "path": "E:\\JYZS\\node_modules\\dotenv"}, {"name": "mongoose", "path": "E:\\JYZS\\node_modules\\mongoose"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\test-redis.js", "name": "test-redis.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [{"name": "dotenv", "path": "E:\\JYZS\\node_modules\\dotenv"}, {"name": "redis", "path": "E:\\JYZS\\node_modules\\redis"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\test-server.js", "name": "test-server.js", "isDirectory": false, "importance": 5, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js"], "packageDependencies": [{"name": "dotenv", "path": "E:\\JYZS\\node_modules\\dotenv"}, {"name": "express", "path": "E:\\JYZS\\node_modules\\express"}, {"name": "cors", "path": "E:\\JYZS\\node_modules\\cors"}, {"name": "helmet", "path": "E:\\JYZS\\node_modules\\helmet"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests", "name": "tests", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\fixtures", "name": "fixtures", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\fixtures\\testData.js", "name": "testData.js", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\auth.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\tasks.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\taskService.test.js"]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers", "name": "helpers", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\setup.js", "name": "setup.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [{"name": "mongoose", "path": "E:\\JYZS\\node_modules\\mongoose"}, {"name": "mongodb-memory-server", "path": "E:\\JYZS\\node_modules\\mongodb-memory-server"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js", "name": "testUtils.js", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\models\\User.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\Task.js"], "packageDependencies": [{"name": "jsonwebtoken", "path": "E:\\JYZS\\node_modules\\jsonwebtoken"}, {"name": "bcryptjs", "path": "E:\\JYZS\\node_modules\\bcryptjs"}, {"name": "mongoose", "path": "E:\\JYZS\\node_modules\\mongoose"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\auth.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\tasks.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\ebbinghausService.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\taskService.test.js"]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\integration", "name": "integration", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\auth.test.js", "name": "auth.test.js", "isDirectory": false, "importance": 5, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\User.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\fixtures\\testData.js"], "packageDependencies": [{"name": "supertest", "path": "E:\\JYZS\\node_modules\\supertest"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\health.test.js", "name": "health.test.js", "isDirectory": false, "importance": 4, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js"], "packageDependencies": [{"name": "supertest", "path": "E:\\JYZS\\node_modules\\supertest"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\tasks.test.js", "name": "tasks.test.js", "isDirectory": false, "importance": 5, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\Task.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\fixtures\\testData.js"], "packageDependencies": [{"name": "supertest", "path": "E:\\JYZS\\node_modules\\supertest"}], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\unit", "name": "unit", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\ebbinghausService.simple.test.js", "name": "ebbinghausService.simple.test.js", "isDirectory": false, "importance": 3, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\services\\ebbinghausService.js"], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\ebbinghausService.test.js", "name": "ebbinghausService.test.js", "isDirectory": false, "importance": 4, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\services\\ebbinghausService.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js"], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\taskService.test.js", "name": "taskService.test.js", "isDirectory": false, "importance": 4, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\Task.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\ReviewSchedule.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\fixtures\\testData.js"], "packageDependencies": [], "dependents": []}]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tsconfig.json", "name": "tsconfig.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system", "name": "ebbinghaus-learning-system", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\.env.development", "name": ".env.development", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\.env.production", "name": ".env.production", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist", "name": "dist", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets", "name": "assets", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ApiTestView-BVxRMBdN.css", "name": "ApiTestView-BVxRMBdN.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ApiTestView-fYkpcgSW.js", "name": "ApiTestView-fYkpcgSW.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ApiTestView-fYkpcgSW.js.map", "name": "ApiTestView-fYkpcgSW.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\AppLayout-BNYRYDsa.js", "name": "AppLayout-BNYRYDsa.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\AppLayout-BNYRYDsa.js.map", "name": "AppLayout-BNYRYDsa.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ComponentTestView-D3DTb0iE.css", "name": "ComponentTestView-D3DTb0iE.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ComponentTestView-D4pzNUcT.js", "name": "ComponentTestView-D4pzNUcT.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ComponentTestView-D4pzNUcT.js.map", "name": "ComponentTestView-D4pzNUcT.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\DashboardView-BNy4ZNFB.js", "name": "DashboardView-BNy4ZNFB.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\DashboardView-BNy4ZNFB.js.map", "name": "DashboardView-BNy4ZNFB.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\DashboardView-DbJudnRo.css", "name": "DashboardView-DbJudnRo.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\HomeView-BucT7Hhy.js", "name": "HomeView-BucT7Hhy.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\HomeView-BucT7Hhy.js.map", "name": "HomeView-BucT7Hhy.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\HomeView-C2oNt8ZK.css", "name": "HomeView-C2oNt8ZK.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-DalhcT85.css", "name": "index-DalhcT85.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-DfjgeC5z.js", "name": "index-DfjgeC5z.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-DfjgeC5z.js.map", "name": "index-DfjgeC5z.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js", "name": "index-Oeuw5Cvn.js", "isDirectory": false, "importance": 5, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\HomeView-BucT7Hhy.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TestView-DWL-s5qg.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ApiTestView-fYkpcgSW.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\StoreTestView-CYNGWrzo.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ComponentTestView-D4pzNUcT.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\DashboardView-BNy4ZNFB.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TasksView-crze5JO2.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TaskDetailView-C3q_qLqY.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewsView-BnKJe1Gi.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewSessionView-F8ureHw_.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapsView-BPcPzjj9.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapDetailView-DwawTnhb.js"], "packageDependencies": [{"name": "util", "path": "E:\\JYZS\\node_modules\\util"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js.map", "name": "index-Oeuw5Cvn.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapCard-33CezlUi.css", "name": "MindMapCard-33CezlUi.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapCard-CqqMxIGg.js", "name": "MindMapCard-CqqMxIGg.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapCard-CqqMxIGg.js.map", "name": "MindMapCard-CqqMxIGg.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapDetailView-DcwAGp1z.css", "name": "MindMapDetailView-DcwAGp1z.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapDetailView-DwawTnhb.js", "name": "MindMapDetailView-DwawTnhb.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapDetailView-DwawTnhb.js.map", "name": "MindMapDetailView-DwawTnhb.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapsView-BPcPzjj9.js", "name": "MindMapsView-BPcPzjj9.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapsView-BPcPzjj9.js.map", "name": "MindMapsView-BPcPzjj9.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapsView-NFrqTSu5.css", "name": "MindMapsView-NFrqTSu5.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapViewer-BPFIGvLL.js", "name": "MindMapViewer-BPFIGvLL.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapViewer-BPFIGvLL.js.map", "name": "MindMapViewer-BPFIGvLL.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewCard-Df-3j-Bk.js", "name": "ReviewCard-Df-3j-Bk.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewCard-Df-3j-Bk.js.map", "name": "ReviewCard-Df-3j-Bk.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewSessionView-DU6ceV1b.css", "name": "ReviewSessionView-DU6ceV1b.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewSessionView-F8ureHw_.js", "name": "ReviewSessionView-F8ureHw_.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewSessionView-F8ureHw_.js.map", "name": "ReviewSessionView-F8ureHw_.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewsView-BnKJe1Gi.js", "name": "ReviewsView-BnKJe1Gi.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewsView-BnKJe1Gi.js.map", "name": "ReviewsView-BnKJe1Gi.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewsView-DV4Z4n6z.css", "name": "ReviewsView-DV4Z4n6z.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\StoreTestView-C3U6VcES.css", "name": "StoreTestView-C3U6VcES.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\StoreTestView-CYNGWrzo.js", "name": "StoreTestView-CYNGWrzo.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\StoreTestView-CYNGWrzo.js.map", "name": "StoreTestView-CYNGWrzo.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TaskDetailView-C3q_qLqY.js", "name": "TaskDetailView-C3q_qLqY.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TaskDetailView-C3q_qLqY.js.map", "name": "TaskDetailView-C3q_qLqY.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TaskDetailView-CuCs1Yr0.css", "name": "TaskDetailView-CuCs1Yr0.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TaskList-Vc7eSG33.js", "name": "TaskList-Vc7eSG33.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TaskList-Vc7eSG33.js.map", "name": "TaskList-Vc7eSG33.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TasksView-CCygG3aS.css", "name": "TasksView-CCygG3aS.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TasksView-crze5JO2.js", "name": "TasksView-crze5JO2.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TasksView-crze5JO2.js.map", "name": "TasksView-crze5JO2.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TestView-DWL-s5qg.js", "name": "TestView-DWL-s5qg.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TestView-DWL-s5qg.js.map", "name": "TestView-DWL-s5qg.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TestView-MHhXiZch.css", "name": "TestView-MHhXiZch.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\docs", "name": "docs", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\docs\\API接口清单.md", "name": "API接口清单.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\docs\\README.md", "name": "README.md", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\docs\\前端接口.md", "name": "前端接口.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\env.d.ts", "name": "env.d.ts", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "vue", "path": "E:\\JYZS\\node_modules\\vue"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\package-lock.json", "name": "package-lock.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\package.json", "name": "package.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src", "name": "src", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\App.vue", "name": "App.vue", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\main.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components", "name": "components", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business", "name": "business", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\MindMapCard.vue", "name": "MindMapCard.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\MindMapEdge.vue", "name": "MindMapEdge.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\MindMapNode.vue", "name": "MindMapNode.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\MindMapViewer.vue", "name": "MindMapViewer.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\ReviewCard.vue", "name": "ReviewCard.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\TaskCard.vue", "name": "TaskCard.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\TaskList.vue", "name": "TaskList.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\common", "name": "common", "isDirectory": true, "children": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\layout", "name": "layout", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\layout\\AppHeader.vue", "name": "AppHeader.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\layout\\AppLayout.vue", "name": "AppLayout.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\layout\\AppNotifications.vue", "name": "AppNotifications.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\layout\\AppSidebar.vue", "name": "AppSidebar.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\mindmap", "name": "mindmap", "isDirectory": true, "children": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\constants", "name": "constants", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\constants\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\main.ts", "name": "main.ts", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\App.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\router", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores", "E:\\JYZS\\ebbinghaus-learning-system\\src\\styles\\index.css"], "packageDependencies": [{"name": "vue", "path": "E:\\JYZS\\node_modules\\vue"}, {"name": "pinia", "path": "E:\\JYZS\\node_modules\\pinia"}, {"name": "element-plus", "path": "E:\\JYZS\\node_modules\\element-plus"}, {"name": "element-plus", "path": "E:\\JYZS\\node_modules\\element-plus\\dist\\index.css"}, {"name": "@element-plus/icons-vue", "path": "E:\\JYZS\\node_modules\\@element-plus\\icons-vue", "scope": "@element-plus"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\router", "name": "router", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\HomeView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TestView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ApiTestView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\StoreTestView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ComponentTestView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\DashboardView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TasksView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TaskDetailView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ReviewsView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ReviewSessionView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\MindMapsView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\MindMapDetailView.vue"], "packageDependencies": [{"name": "vue-router", "path": "E:\\JYZS\\node_modules\\vue-router"}], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services", "name": "services", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api", "name": "api", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\analytics.ts", "name": "analytics.ts", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "E:\\JYZS\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 7, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\task.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\review.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\mindmap.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\user.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\notification.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\analytics.ts"], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\mindmap.ts", "name": "mindmap.ts", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "E:\\JYZS\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\notification.ts", "name": "notification.ts", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "E:\\JYZS\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\review.ts", "name": "review.ts", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "E:\\JYZS\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\task.ts", "name": "task.ts", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "E:\\JYZS\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\user.ts", "name": "user.ts", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "E:\\JYZS\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}]}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores", "name": "stores", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts", "name": "app.ts", "isDirectory": false, "importance": 9, "dependencies": [], "packageDependencies": [{"name": "pinia", "path": "E:\\JYZS\\node_modules\\pinia"}, {"name": "vue", "path": "E:\\JYZS\\node_modules\\vue"}, {"name": "@/types", "path": "E:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\mindmap.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\review.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\task.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\user.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 7, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\user.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\task.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\review.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\mindmap.ts"], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\mindmap.ts", "name": "mindmap.ts", "isDirectory": false, "importance": 6, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts"], "packageDependencies": [{"name": "pinia", "path": "E:\\JYZS\\node_modules\\pinia"}, {"name": "vue", "path": "E:\\JYZS\\node_modules\\vue"}, {"name": "@/services", "path": "E:\\JYZS\\node_modules\\@\\services\\api", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\review.ts", "name": "review.ts", "isDirectory": false, "importance": 6, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts"], "packageDependencies": [{"name": "pinia", "path": "E:\\JYZS\\node_modules\\pinia"}, {"name": "vue", "path": "E:\\JYZS\\node_modules\\vue"}, {"name": "@/services", "path": "E:\\JYZS\\node_modules\\@\\services\\api", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\task.ts", "name": "task.ts", "isDirectory": false, "importance": 6, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts"], "packageDependencies": [{"name": "pinia", "path": "E:\\JYZS\\node_modules\\pinia"}, {"name": "vue", "path": "E:\\JYZS\\node_modules\\vue"}, {"name": "@/services", "path": "E:\\JYZS\\node_modules\\@\\services\\api", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\user.ts", "name": "user.ts", "isDirectory": false, "importance": 6, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts"], "packageDependencies": [{"name": "pinia", "path": "E:\\JYZS\\node_modules\\pinia"}, {"name": "vue", "path": "E:\\JYZS\\node_modules\\vue"}, {"name": "@/services", "path": "E:\\JYZS\\node_modules\\@\\services\\api", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts"]}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\styles", "name": "styles", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\styles\\index.css", "name": "index.css", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\main.ts"]}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\types", "name": "types", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\types\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\utils", "name": "utils", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\utils\\http.ts", "name": "http.ts", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "axios", "path": "E:\\JYZS\\node_modules\\axios"}, {"name": "element-plus", "path": "E:\\JYZS\\node_modules\\element-plus"}, {"name": "@/types", "path": "E:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\utils\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 6, "dependencies": [], "packageDependencies": [{"name": "dayjs", "path": "E:\\JYZS\\node_modules\\dayjs"}, {"name": "dayjs", "path": "E:\\JYZS\\node_modules\\dayjs\\locale\\zh-cn"}, {"name": "dayjs", "path": "E:\\JYZS\\node_modules\\dayjs\\plugin\\relativeTime"}, {"name": "dayjs", "path": "E:\\JYZS\\node_modules\\dayjs\\plugin\\duration"}, {"name": "@/types", "path": "E:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views", "name": "views", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\analytics", "name": "analytics", "isDirectory": true, "children": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ApiTestView.vue", "name": "ApiTestView.vue", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ComponentTestView.vue", "name": "ComponentTestView.vue", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\dashboard", "name": "dashboard", "isDirectory": true, "children": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\DashboardView.vue", "name": "DashboardView.vue", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\HomeView.vue", "name": "HomeView.vue", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\mindmap", "name": "mindmap", "isDirectory": true, "children": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\MindMapDetailView.vue", "name": "MindMapDetailView.vue", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\MindMapsView.vue", "name": "MindMapsView.vue", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\review", "name": "review", "isDirectory": true, "children": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ReviewSessionView.vue", "name": "ReviewSessionView.vue", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ReviewsView.vue", "name": "ReviewsView.vue", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\StoreTestView.vue", "name": "StoreTestView.vue", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\task", "name": "task", "isDirectory": true, "children": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TaskDetailView.vue", "name": "TaskDetailView.vue", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TasksView.vue", "name": "TasksView.vue", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TestView.vue", "name": "TestView.vue", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}]}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\tsconfig.app.json", "name": "tsconfig.app.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\tsconfig.json", "name": "tsconfig.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\tsconfig.node.json", "name": "tsconfig.node.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\tsconfig.vitest.json", "name": "tsconfig.vitest.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\vite.config.ts", "name": "vite.config.ts", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "vite", "path": "E:\\JYZS\\node_modules\\vite"}, {"name": "@vitejs/plugin-vue", "path": "E:\\JYZS\\node_modules\\@vitejs\\plugin-vue", "scope": "@vitejs"}, {"name": "node:url", "path": "E:\\JYZS\\node_modules\\node:url"}], "dependents": []}]}, {"path": "E:\\JYZS\\设计开发标准doc", "name": "设计开发标准doc", "isDirectory": true, "children": [{"path": "E:\\JYZS\\设计开发标准doc\\00-术语表.md", "name": "00-术语表.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\01-需求分析", "name": "01-需求分析", "isDirectory": true, "children": [{"path": "E:\\JYZS\\设计开发标准doc\\01-需求分析\\01-项目背景与目标.md", "name": "01-项目背景与目标.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\01-需求分析\\02-功能需求规格.md", "name": "02-功能需求规格.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\01-需求分析\\03-用户场景与流程.md", "name": "03-用户场景与流程.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\01-需求分析\\04-非功能性需求.md", "name": "04-非功能性需求.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\01-需求分析\\05-需求优先级与验收标准.md", "name": "05-需求优先级与验收标准.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\01-需求分析\\06-业务规则索引.md", "name": "06-业务规则索引.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\01-需求分析\\README.md", "name": "README.md", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\设计开发标准doc\\02-系统设计", "name": "02-系统设计", "isDirectory": true, "children": [{"path": "E:\\JYZS\\设计开发标准doc\\02-系统设计\\01-系统整体架构设计.md", "name": "01-系统整体架构设计.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\02-系统设计\\02-任务管理核心模块设计.md", "name": "02-任务管理核心模块设计.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\02-系统设计\\03-智能时间管理模块设计.md", "name": "03-智能时间管理模块设计.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\02-系统设计\\04-思维导图功能模块设计.md", "name": "04-思维导图功能模块设计.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\02-系统设计\\05-用户界面设计规范.md", "name": "05-用户界面设计规范.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\02-系统设计\\06-数据存储与同步设计.md", "name": "06-数据存储与同步设计.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\02-系统设计\\07-模块协作与通信规范.md", "name": "07-模块协作与通信规范.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\02-系统设计\\08-API接口设计.md", "name": "08-API接口设计.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\02-系统设计\\09-数据模型设计.md", "name": "09-数据模型设计.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\02-系统设计\\README.md", "name": "README.md", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\设计开发标准doc\\03-开发实现", "name": "03-开发实现", "isDirectory": true, "children": [{"path": "E:\\JYZS\\设计开发标准doc\\03-开发实现\\01-前端核心功能开发指南.md", "name": "01-前端核心功能开发指南.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\设计开发标准doc\\03-开发实现\\02-后端核心功能开发指南.md", "name": "02-后端核心功能开发指南.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}]}]}]}}