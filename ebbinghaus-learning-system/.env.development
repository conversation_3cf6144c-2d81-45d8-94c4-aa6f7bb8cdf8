# 开发环境配置
NODE_ENV=development

# API配置 - 使用相对路径通过Vite代理
VITE_API_BASE_URL=/api
VITE_API_TIMEOUT=10000

# 应用配置
VITE_APP_TITLE=艾宾浩斯记忆曲线学习管理系统
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=基于艾宾浩斯记忆曲线的智能学习管理系统

# 文件上传配置
VITE_UPLOAD_URL=http://localhost:3004/api/upload
VITE_MAX_FILE_SIZE=10485760

# WebSocket配置
VITE_WS_URL=ws://localhost:3004/ws

# 第三方服务配置
VITE_ANALYTICS_ID=
VITE_SENTRY_DSN=

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_TRACKING=false

# 调试配置
VITE_DEBUG=true
VITE_LOG_LEVEL=debug
