{"name": "read-package-json-fast", "version": "3.0.2", "description": "Like read-package-json, but faster", "main": "lib/index.js", "author": "GitHub Inc.", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.11.0", "tap": "^16.3.0"}, "dependencies": {"json-parse-even-better-errors": "^3.0.0", "npm-normalize-package-bin": "^3.0.0"}, "repository": {"type": "git", "url": "https://github.com/npm/read-package-json-fast.git"}, "files": ["bin/", "lib/"], "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.11.0"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}