{"name": "npm-normalize-package-bin", "version": "3.0.1", "description": "Turn any flavor of allowable package.json bin into a normalized object", "main": "lib/index.js", "repository": {"type": "git", "url": "https://github.com/npm/npm-normalize-package-bin.git"}, "author": "GitHub Inc.", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.14.1", "tap": "^16.3.0"}, "files": ["bin/", "lib/"], "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.14.1", "publish": "true"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}