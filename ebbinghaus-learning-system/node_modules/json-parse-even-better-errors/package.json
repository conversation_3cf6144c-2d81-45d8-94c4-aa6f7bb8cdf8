{"name": "json-parse-even-better-errors", "version": "3.0.2", "description": "JSON.parse with context information on error", "main": "lib/index.js", "files": ["bin/", "lib/"], "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/npm/json-parse-even-better-errors.git"}, "keywords": ["JSON", "parser"], "author": "GitHub Inc.", "license": "MIT", "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.22.0", "tap": "^16.3.0"}, "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.22.0", "publish": true}}