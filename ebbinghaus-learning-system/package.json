{"name": "ebbinghaus-learning-system", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "dev": "vite"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "cytoscape": "^3.28.1", "cytoscape-dagre": "^2.5.0", "cytoscape-cose-bilkent": "^4.1.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@playwright/test": "^1.40.0", "@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/jsdom": "^21.1.6", "@types/node": "^18.19.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.0", "@vue/tsconfig": "^0.5.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^23.0.1", "npm-run-all2": "^6.1.1", "prettier": "^3.0.3", "typescript": "~5.3.0", "vite": "^5.0.10", "vitest": "^1.0.4", "vue-tsc": "^1.8.25", "@types/lodash-es": "^4.17.12", "@types/cytoscape": "^3.19.16", "tailwindcss": "^3.3.6", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}}